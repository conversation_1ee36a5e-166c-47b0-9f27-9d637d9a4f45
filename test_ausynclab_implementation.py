#!/usr/bin/env python3
"""
Test script for AusyncLab TTS implementation
"""

def test_constants():
    """Test that constants are properly defined"""
    print("=== Testing Constants ===")
    
    from videotrans.tts import AUSYNCLAB_TTS, TTS_NAME_LIST
    
    print(f"AUSYNCLAB_TTS = {AUSYNCLAB_TTS}")
    assert AUSYNCLAB_TTS == 17, f"Expected 17, got {AUSYNCLAB_TTS}"
    
    print(f"TTS_NAME_LIST length = {len(TTS_NAME_LIST)}")
    assert len(TTS_NAME_LIST) == 18, f"Expected 18 items, got {len(TTS_NAME_LIST)}"
    
    print(f"Last TTS service = {TTS_NAME_LIST[-1]}")
    assert TTS_NAME_LIST[-1] == "AusyncLab TTS", f"Expected 'AusyncLab TTS', got {TTS_NAME_LIST[-1]}"
    
    print("✓ Constants test passed")


def test_language_support():
    """Test language support validation"""
    print("\n=== Testing Language Support ===")
    
    from videotrans.tts import is_allow_lang, AUSYNCLAB_TTS
    
    # Test supported languages
    supported_langs = ['en', 'vi', 'de', 'fr', 'ko', 'ja', 'zh', 'it', 'pt', 'pl', 'es', 'nl', 'th', 'ms', 'hi', 'ar', 'he']
    
    for lang in supported_langs:
        result = is_allow_lang(langcode=lang, tts_type=AUSYNCLAB_TTS)
        assert result is True, f"Language {lang} should be supported but got: {result}"
        print(f"✓ {lang} is supported")
    
    # Test unsupported language
    result = is_allow_lang(langcode='xx', tts_type=AUSYNCLAB_TTS)
    assert result != True, f"Language 'xx' should not be supported but got: {result}"
    print(f"✓ Unsupported language 'xx' correctly rejected: {result}")
    
    print("✓ Language support test passed")


def test_api_key_validation():
    """Test API key validation"""
    print("\n=== Testing API Key Validation ===")
    
    from videotrans.tts import is_input_api, AUSYNCLAB_TTS
    from videotrans.configure import config
    
    # Save original key
    original_key = config.params.get('ausynclab_key', '')
    
    try:
        # Test without API key
        config.params['ausynclab_key'] = ''
        result = is_input_api(tts_type=AUSYNCLAB_TTS, return_str=True)
        assert result == "Please configure the AusyncLab API key first.", f"Expected API key error, got: {result}"
        print("✓ API key validation works correctly")
        
        # Test with API key
        config.params['ausynclab_key'] = 'test_key'
        result = is_input_api(tts_type=AUSYNCLAB_TTS, return_str=True)
        assert result is True, f"Expected True with API key, got: {result}"
        print("✓ API key validation passes with key")
        
    finally:
        # Restore original key
        config.params['ausynclab_key'] = original_key
    
    print("✓ API key validation test passed")


def test_voice_list_function():
    """Test voice list retrieval function"""
    print("\n=== Testing Voice List Function ===")
    
    from videotrans.util.tools import get_ausynclab_rolelist
    
    # Test without API key (should return empty dict)
    result = get_ausynclab_rolelist()
    assert isinstance(result, dict), f"Expected dict, got {type(result)}"
    print(f"✓ Voice list function returns dict: {result}")
    
    print("✓ Voice list function test passed")


def test_voice_id_extraction():
    """Test voice ID extraction from role names"""
    print("\n=== Testing Voice ID Extraction ===")
    
    from videotrans.tts._ausynclab import AusyncLabTTS
    
    # Create instance for testing
    tts = AusyncLabTTS(queue_tts=[{'text': 'test', 'filename': 'test.wav'}])
    
    # Test direct ID
    assert tts._get_voice_id("123") == 123
    print("✓ Direct ID extraction works")
    
    # Test ID from formatted string
    assert tts._get_voice_id("Voice Name (ID: 456)") == 456
    print("✓ Formatted ID extraction works")
    
    # Test default mapping
    assert tts._get_voice_id("female") == 1
    assert tts._get_voice_id("male") == 2
    assert tts._get_voice_id("unknown") == 1
    print("✓ Default mapping works")
    
    print("✓ Voice ID extraction test passed")


def test_tts_dispatcher():
    """Test TTS dispatcher includes AusyncLab"""
    print("\n=== Testing TTS Dispatcher ===")
    
    from videotrans.tts import AUSYNCLAB_TTS
    
    # Test that the dispatcher would route to AusyncLab
    # We can't actually run it without proper setup, but we can verify the import works
    try:
        from videotrans.tts._ausynclab import AusyncLabTTS
        print("✓ AusyncLabTTS class can be imported")
        
        # Test class instantiation
        tts = AusyncLabTTS(queue_tts=[{'text': 'test', 'filename': 'test.wav'}])
        assert hasattr(tts, '_exec'), "AusyncLabTTS should have _exec method"
        assert hasattr(tts, '_item_task'), "AusyncLabTTS should have _item_task method"
        print("✓ AusyncLabTTS class can be instantiated")
        
    except ImportError as e:
        print(f"✗ Failed to import AusyncLabTTS: {e}")
        raise
    
    print("✓ TTS dispatcher test passed")


def main():
    """Run all tests"""
    print("Testing AusyncLab TTS Implementation")
    print("=" * 50)
    
    try:
        test_constants()
        test_language_support()
        test_api_key_validation()
        test_voice_list_function()
        test_voice_id_extraction()
        test_tts_dispatcher()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed! AusyncLab TTS implementation is working correctly.")
        print("\nNext steps:")
        print("1. Configure your AusyncLab API key")
        print("2. Test with actual API calls")
        print("3. Use tts_type=17 in your API requests")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
