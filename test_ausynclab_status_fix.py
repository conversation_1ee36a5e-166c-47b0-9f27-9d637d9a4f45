#!/usr/bin/env python3
"""
Test script to demonstrate how to fix the status issue with AusyncLab TTS
"""

import os
import tempfile
from pathlib import Path


def test_status_issue():
    """Demonstrate the status issue and solutions"""
    print("=== Testing Status Issue and Solutions ===")
    
    from videotrans.configure import config
    from videotrans import tts
    
    # Show current status
    print(f"Current config.current_status: {config.current_status}")
    print(f"Current config.box_tts: {config.box_tts}")
    
    # Create a test TTS queue
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp:
        test_queue = [{
            "text": "Hello, this is a test of AusyncLab TTS",
            "role": "1",  # Use voice ID 1
            "filename": tmp.name,
            "tts_type": tts.AUSYNCLAB_TTS
        }]
        
        print(f"\nTest queue created with output file: {tmp.name}")
        
        # Test 1: Try with current status (should fail)
        print("\n--- Test 1: Current status (should fail) ---")
        try:
            result = tts.run(
                queue_tts=test_queue,
                language="en",
                is_test=False  # Don't bypass status check
            )
            print("✗ TTS ran (unexpected - should have been blocked)")
        except Exception as e:
            print(f"✓ TTS was blocked as expected: {e}")
        
        # Check if function returned early (no exception but no file created)
        if not Path(tmp.name).exists() or Path(tmp.name).stat().st_size == 0:
            print("✓ TTS was blocked by status check (function returned early)")
        
        # Test 2: Use is_test=True to bypass status check
        print("\n--- Test 2: Using is_test=True (should work) ---")
        try:
            result = tts.run(
                queue_tts=test_queue,
                language="en",
                is_test=True  # Bypass status check
            )
            print("✓ TTS ran with is_test=True")
            
            if Path(tmp.name).exists() and Path(tmp.name).stat().st_size > 0:
                print(f"✓ Audio file created: {Path(tmp.name).stat().st_size} bytes")
            else:
                print("✗ Audio file not created (may need API key)")
                
        except Exception as e:
            print(f"✗ TTS failed: {e}")
        
        # Test 3: Set box_tts to 'ing' 
        print("\n--- Test 3: Setting box_tts='ing' (should work) ---")
        original_box_tts = config.box_tts
        try:
            config.box_tts = 'ing'
            print(f"Set config.box_tts to: {config.box_tts}")
            
            result = tts.run(
                queue_tts=test_queue,
                language="en",
                is_test=False  # Don't bypass status check
            )
            print("✓ TTS ran with box_tts='ing'")
            
        except Exception as e:
            print(f"✗ TTS failed: {e}")
        finally:
            # Restore original status
            config.box_tts = original_box_tts
            print(f"Restored config.box_tts to: {config.box_tts}")
        
        # Test 4: Set current_status to 'ing'
        print("\n--- Test 4: Setting current_status='ing' (should work) ---")
        original_current_status = config.current_status
        try:
            config.current_status = 'ing'
            print(f"Set config.current_status to: {config.current_status}")
            
            result = tts.run(
                queue_tts=test_queue,
                language="en",
                is_test=False  # Don't bypass status check
            )
            print("✓ TTS ran with current_status='ing'")
            
        except Exception as e:
            print(f"✗ TTS failed: {e}")
        finally:
            # Restore original status
            config.current_status = original_current_status
            print(f"Restored config.current_status to: {config.current_status}")
        
        # Cleanup
        try:
            os.unlink(tmp.name)
        except:
            pass


def show_recommended_solutions():
    """Show recommended solutions for different use cases"""
    print("\n" + "=" * 60)
    print("RECOMMENDED SOLUTIONS")
    print("=" * 60)
    
    print("\n1. **For Testing/Development:**")
    print("   Use is_test=True to bypass status checks")
    print("   ```python")
    print("   tts.run(queue_tts=queue, language='en', is_test=True)")
    print("   ```")
    
    print("\n2. **For API/Toolbox Usage:**")
    print("   Set box_tts='ing' before calling TTS")
    print("   ```python")
    print("   config.box_tts = 'ing'")
    print("   tts.run(queue_tts=queue, language='en')")
    print("   config.box_tts = 'stop'  # Reset when done")
    print("   ```")
    
    print("\n3. **For Main Workflow:**")
    print("   Set current_status='ing' during video processing")
    print("   ```python")
    print("   config.current_status = 'ing'")
    print("   tts.run(queue_tts=queue, language='en')")
    print("   config.current_status = 'stop'  # Reset when done")
    print("   ```")
    
    print("\n4. **For API Endpoints:**")
    print("   The API endpoints already handle this correctly:")
    print("   - /tts endpoint sets config.box_tts = 'ing'")
    print("   - /v2/trans_video endpoint manages status properly")
    
    print("\n5. **Status Check Logic:**")
    print("   The TTS will run if ANY of these conditions are true:")
    print("   - is_test=True")
    print("   - config.current_status == 'ing'")
    print("   - config.box_tts == 'ing'")
    print("   - config.exit_soft == False (default)")


def main():
    """Run the status test and show solutions"""
    print("AusyncLab TTS Status Issue Test")
    print("=" * 50)
    
    try:
        test_status_issue()
        show_recommended_solutions()
        
        print("\n" + "=" * 50)
        print("✅ Status issue analysis complete!")
        print("\nThe issue is that config.current_status='stop' blocks TTS execution.")
        print("Use one of the recommended solutions above to fix it.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
