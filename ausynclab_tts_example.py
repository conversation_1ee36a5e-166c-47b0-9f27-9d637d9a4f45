#!/usr/bin/env python3
"""
Example usage of AusyncLab TTS service in pyvideotrans

This script demonstrates how to:
1. Get the voice list for AusyncLab TTS
2. Use AusyncLab TTS in video dubbing workflow
"""

import requests
import json


def test_voice_list_api():
    """Test the voice list API endpoint for AusyncLab TTS"""
    print("=== Testing Voice List API ===")
    
    url = "http://127.0.0.1:9011/voice_list"
    params = {
        "tts_type": 17  # AusyncLab TTS
    }
    
    try:
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ API Response: {json.dumps(data, indent=2)}")
            
            if data.get("code") == 0:
                voices = data.get("data", {}).get("voices", [])
                print(f"✓ Found {len(voices)} voices")
                if voices:
                    print(f"✓ Sample voices: {voices[:3]}")
            else:
                print(f"✗ API returned error: {data.get('msg')}")
        else:
            print(f"✗ HTTP Error: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("✗ Connection failed - make sure the API server is running on port 9011")
    except Exception as e:
        print(f"✗ Error: {e}")


def test_voice_list_with_language_filter():
    """Test voice list API with language filtering"""
    print("\n=== Testing Voice List with Language Filter ===")
    
    url = "http://127.0.0.1:9011/voice_list"
    
    # Test different language filters
    languages = ["en", "zh", "vi", "ja"]
    
    for lang in languages:
        params = {
            "tts_type": 17,  # AusyncLab TTS
            "language": lang
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    voices = data.get("data", {}).get("voices", [])
                    print(f"✓ {lang}: {len(voices)} voices")
                else:
                    print(f"✗ {lang}: {data.get('msg')}")
            else:
                print(f"✗ {lang}: HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"✗ {lang}: Connection failed")
            break
        except Exception as e:
            print(f"✗ {lang}: {e}")


def example_video_dubbing_request():
    """Example of how to use AusyncLab TTS in video dubbing"""
    print("\n=== Example Video Dubbing Request ===")
    
    # This is an example payload for the /v2/trans_video endpoint
    example_payload = {
        "task": {
            "id": "example_task_123",
            "videoUrl": "https://example.com/video.mp4",
            "name": "Example Video",
            "duration": 120,
            "thumbnail": "https://example.com/thumb.jpg"
        },
        "translation": {
            "voiceSeparation": False,
            "sourceLanguage": "en",
            "targetLanguage": "zh",
            "subtitleLayout": "translation"
        },
        "subtitleStyle": {
            "translation": {
                "fontFamily": "Arial",
                "fontSize": 16,
                "fontColor": "#FFFFFF",
                "strokeColor": "#000000",
                "strokeWidth": 1,
                "backgroundColor": "#000000",
                "backgroundOpacity": 0.5,
                "alignment": "bottom",
                "marginV": 20
            }
        },
        "tts": {
            "service": 17,  # AusyncLab TTS
            "voice": "Sample Voice (ID: 1)",  # Voice from voice list
            "speed": 1.0,
            "model": "myna-2"
        },
        "subtitle_url": "https://example.com/subtitles.srt"
    }
    
    print("Example request payload:")
    print(json.dumps(example_payload, indent=2))
    
    print("\nTo use this:")
    print("1. Replace videoUrl with your actual video URL")
    print("2. Replace subtitle_url with your SRT file URL")
    print("3. Get available voices using the voice list API")
    print("4. Choose a voice and update the 'voice' field")
    print("5. Configure your AusyncLab API key in the system")
    print("6. Send POST request to /v2/trans_video endpoint")


def configuration_example():
    """Show how to configure AusyncLab TTS"""
    print("\n=== Configuration Example ===")
    
    print("To configure AusyncLab TTS:")
    print("1. Get API key from https://www.ausynclab.io/")
    print("2. Set the API key in your configuration:")
    print("   - Via GUI: Use the AusyncLab TTS configuration window")
    print("   - Via API: Update params.json with 'ausynclab_key'")
    print("   - Via code: config.params['ausynclab_key'] = 'your_api_key'")
    
    print("\n3. Supported languages:")
    languages = [
        "en (English)", "vi (Vietnamese)", "de (German)", "fr (French)",
        "ko (Korean)", "ja (Japanese)", "zh (Chinese)", "it (Italian)",
        "pt (Portuguese)", "pl (Polish)", "es (Spanish)", "nl (Dutch)",
        "th (Thai)", "ms (Malay)", "hi (Hindi)", "ar (Arabic)", "he (Hebrew)"
    ]
    for lang in languages:
        print(f"   - {lang}")


def main():
    """Run all examples"""
    print("AusyncLab TTS Integration Example")
    print("=" * 50)
    
    # Test voice list API
    test_voice_list_api()
    
    # Test with language filters
    test_voice_list_with_language_filter()
    
    # Show example usage
    example_video_dubbing_request()
    
    # Show configuration
    configuration_example()
    
    print("\n" + "=" * 50)
    print("Example completed!")
    print("\nFor more information:")
    print("- AusyncLab API docs: https://docs.ausynclab.io/tts")
    print("- Voice library: https://docs.ausynclab.io/voices")


if __name__ == "__main__":
    main()
